import request, { analysisRes } from '../request';
import config from '../config';

const { vehicle } = config.apiUrls;

export default {
  // 获取员工关联的车辆信息
  async getEmployeeVehicle(employeeId) {
    const res = await request.get(vehicle.getEmployeeVehicle.replace('{employeeId}', employeeId), {
      hideLoading: true,
    });
    const data = analysisRes(res);
    return data;
  },

  // 更新车辆信息
  async updateVehicleInfo(vehicleId, employeeId, vehicleData) {
    const res = await request.put(
      `${vehicle.updateVehicleInfo.replace('{vehicleId}', vehicleId)}?employeeId=${employeeId}`,
      {
        ...vehicleData,
        loadingText: '更新中...',
      }
    );
    const data = analysisRes(res);
    return data;
  },

  // 更新车辆状态
  async updateVehicleStatus(vehicleId, employeeId, statusData) {
    const res = await request.put(
      `${vehicle.updateVehicleStatus.replace('{vehicleId}', vehicleId)}?employeeId=${employeeId}`,
      {
        ...statusData,
        loadingText: '更新状态中...',
      }
    );
    const data = analysisRes(res);
    return data;
  },

  // 获取车辆详细信息
  async getVehicleDetail(vehicleId) {
    const res = await request.get(vehicle.getVehicleDetail.replace('{vehicleId}', vehicleId), {
      hideLoading: true,
    });
    const data = analysisRes(res);
    return data;
  },
};
