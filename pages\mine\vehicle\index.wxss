/* pages/mine/vehicle/index.wxss */
.container {
  background-color: #f5f5f5;
  min-height: 100vh;
  padding-bottom: 40rpx;
}



.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 400rpx;
}

.loading-text {
  color: #999;
  font-size: 28rpx;
}

.vehicle-container {
  padding: 20rpx;
  padding-top: 20rpx; /* 导航栏已经有自动间距 */
}

.info-card {
  background-color: white;
  border-radius: 20rpx;
  margin-bottom: 24rpx;
  padding: 40rpx 32rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 32rpx;
  padding-bottom: 24rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 12rpx;
}

.card-icon {
  font-size: 32rpx;
}

.card-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

/* 车辆摘要样式 */
.vehicle-summary {
  padding: 20rpx 0;
}

.plate-number {
  text-align: center;
  margin-bottom: 24rpx;
}

.plate-text {
  display: inline-block;
  background: linear-gradient(135deg, #007aff, #0056cc);
  color: white;
  padding: 16rpx 32rpx;
  border-radius: 12rpx;
  font-size: 36rpx;
  font-weight: bold;
  letter-spacing: 4rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 122, 255, 0.3);
}

.vehicle-meta {
  display: flex;
  justify-content: space-around;
  gap: 20rpx;
}

.meta-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8rpx;
  flex: 1;
  padding: 16rpx;
  background-color: #f8f9fa;
  border-radius: 12rpx;
}

.meta-icon {
  font-size: 28rpx;
}

.meta-text {
  font-size: 26rpx;
  color: #666;
  text-align: center;
}

.card-actions {
  display: flex;
  gap: 20rpx;
}

.btn-edit {
  display: flex;
  align-items: center;
  gap: 8rpx;
  background-color: #007aff;
  color: white;
  border: none;
  border-radius: 12rpx;  
  font-size: 26rpx;
  transition: all 0.3s ease;
  outline: none;
  box-sizing: border-box;
}

.btn-edit::after {
  border: none;
}

.btn-edit:active {
  background-color: #0056cc;
  transform: scale(0.98);
}

.edit-actions {
  display: flex;
  gap: 20rpx;
}

.btn-cancel {
  display: flex;
  align-items: center;
  gap: 8rpx;
  background-color: #f5f5f5;
  color: #666;
  border: none;
  border-radius: 12rpx;  
  font-size: 26rpx;
  transition: all 0.3s ease;
  outline: none;
  box-sizing: border-box;
}

.btn-cancel::after {
  border: none;
}

.btn-cancel:active {
  background-color: #e0e0e0;
  transform: scale(0.98);
}

.btn-save {
  display: flex;
  align-items: center;
  gap: 8rpx;
  background-color: #007aff;
  color: white;
  border: none;
  border-radius: 12rpx;  
  font-size: 26rpx;
  transition: all 0.3s ease;
  outline: none;
  box-sizing: border-box;
}

.btn-save::after {
  border: none;
}

.btn-save:active {
  background-color: #0056cc;
  transform: scale(0.98);
}

.btn-icon {
  font-size: 24rpx;
}

/* 表单项样式 */
.form-item {
  margin-bottom: 32rpx;
  padding: 24rpx;
  background-color: #fafafa;
  border-radius: 16rpx;
  border: 1rpx solid #f0f0f0;
}

.item-header {
  display: flex;
  align-items: baseline;
  margin-bottom: 16rpx;
  gap: 8rpx;
}

.item-title {
  color: #333;
  font-size: 30rpx;
  font-weight: 600;
  line-height: 1.4;
}

.item-unit {
  color: #999;
  font-size: 24rpx;
  font-weight: normal;
}

.item-content {
  width: 100%;
}

.item-value {
  color: #666;
  font-size: 28rpx;
  line-height: 1.6;
  word-break: break-all;
  min-height: 40rpx;
  display: block;
}

/* 兼容旧样式 */
.info-row {
  display: flex;
  flex-direction: column;
  margin-bottom: 20rpx;
}

.label {
  color: #333;
  font-size: 28rpx;
  font-weight: 500;
  margin-bottom: 8rpx;
  line-height: 1.5;
}

.value {
  color: #666;
  font-size: 28rpx;
  line-height: 1.5;
  word-break: break-all;
  min-height: 40rpx;
  padding: 8rpx 0;
}

.form-input {
  width: 100%;
  height: 2.4rem;
  padding: 0 20rpx;
  border: 1rpx solid #e8e8e8;
  border-radius: 12rpx;
  font-size: 28rpx;
  background-color: #fff;
  box-sizing: border-box;
  transition: all 0.3s ease;
  color: #333;
  line-height: 1.4;
}

.form-input:focus {
  border-color: #007aff;
  background-color: #fff;
  box-shadow: 0 0 0 2rpx rgba(0, 122, 255, 0.1);
}

.form-textarea {
  width: 100%;
  min-height: 120rpx;
  padding: 16rpx 20rpx;
  border: 1rpx solid #e8e8e8;
  border-radius: 12rpx;
  font-size: 28rpx;
  background-color: #fff;
  box-sizing: border-box;
  transition: all 0.3s ease;
  resize: none;
  color: #333;
  line-height: 1.6;
}

.form-textarea:focus {
  border-color: #007aff;
  background-color: #fff;
  box-shadow: 0 0 0 2rpx rgba(0, 122, 255, 0.1);
}

.date-picker {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16rpx 20rpx;
  border: 1rpx solid #e8e8e8;
  border-radius: 12rpx;
  background-color: #fff;
  transition: all 0.3s ease;
  min-height: 48rpx;
}

.date-picker:active {
  background-color: #f8f9fa;
  border-color: #007aff;
}

.date-text {
  font-size: 28rpx;
  color: #333;
  flex: 1;
  line-height: 1.4;
}

.date-text.placeholder {
  color: #999;
}

.icon-arrow {
  color: #999;
  font-size: 24rpx;
  margin-left: 16rpx;
}

.status-idle {
  color: #52c41a;
}

.status-in-use {
  color: #1890ff;
}

.status-maintenance {
  color: #faad14;
}

.status-disabled {
  color: #f5222d;
}

/* 更新记录样式 */
.update-record {
  background: linear-gradient(135deg, #f8f9ff, #f0f4ff);
  border-left: 4rpx solid #007aff;
}

.update-info {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.update-item {
  display: flex;
  align-items: flex-start;
  gap: 16rpx;
  padding: 16rpx;
  background-color: rgba(255, 255, 255, 0.8);
  border-radius: 12rpx;
}

.update-icon {
  font-size: 28rpx;
  margin-top: 4rpx;
}

.update-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 4rpx;
}

.update-label {
  font-size: 24rpx;
  color: #999;
  font-weight: 500;
}

.update-value {
  font-size: 28rpx;
  color: #333;
  word-break: break-all;
}

/* 表单聚焦效果 */
.form-input::placeholder,
.form-textarea::placeholder {
  color: #999;
}

/* 卡片阴影优化 */
.info-card:hover {
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.15);
}

/* 状态颜色增强 */
.status-idle {
  background-color: rgba(82, 196, 26, 0.1);
  padding: 4rpx 12rpx;
  border-radius: 8rpx;
  font-weight: 500;
}

.status-in-use {
  background-color: rgba(24, 144, 255, 0.1);
  padding: 4rpx 12rpx;
  border-radius: 8rpx;
  font-weight: 500;
}

.status-maintenance {
  background-color: rgba(250, 173, 20, 0.1);
  padding: 4rpx 12rpx;
  border-radius: 8rpx;
  font-weight: 500;
}

.status-disabled {
  background-color: rgba(245, 34, 45, 0.1);
  padding: 4rpx 12rpx;
  border-radius: 8rpx;
  font-weight: 500;
}

.status-custom {
  background-color: rgba(128, 128, 128, 0.1);
  padding: 4rpx 12rpx;
  border-radius: 8rpx;
  font-weight: 500;
  color: #666;
}

/* 状态点击提示 */
.meta-item {
  cursor: pointer;
  position: relative;
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 8rpx 12rpx;
  border-radius: 8rpx;
  transition: background-color 0.3s ease;
}

.meta-item:hover {
  background-color: rgba(0, 0, 0, 0.05);
}

.edit-hint {
  font-size: 20rpx;
  color: #1890ff;
  margin-left: 8rpx;
  opacity: 0.8;
}

/* 状态修改弹窗样式 */
.status-modal-mask {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
}

.status-modal-container {
  width: 80%;
  max-width: 600rpx;
  background: white;
  border-radius: 20rpx;
  overflow: hidden;
}

.status-modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 32rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.status-modal-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.status-modal-close {
  width: 48rpx;
  height: 48rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 36rpx;
  color: #999;
  cursor: pointer;
}

.status-modal-body {
  padding: 32rpx;
}

.section-title {
  display: block;
  font-size: 28rpx;
  color: #333;
  margin-bottom: 20rpx;
  font-weight: 500;
}

.quick-status-section {
  margin-bottom: 40rpx;
}

.quick-status-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
}

.quick-status-btn {
  padding: 16rpx 24rpx;
  background: #f5f5f5;
  border-radius: 12rpx;
  font-size: 26rpx;
  color: #666;
  cursor: pointer;
  transition: all 0.3s ease;
}

.quick-status-btn.active {
  background: #1890ff;
  color: white;
}

.custom-status-section {
  position: relative;
}

.custom-status-input {
  width: 100%;
  height: auto;
  padding: 20rpx;
  border: 1rpx solid #e0e0e0;
  border-radius: 12rpx;
  font-size: 28rpx;
  box-sizing: border-box;
}

.custom-status-input:focus {
  border-color: #1890ff;
}

.char-count {
  text-align: right;
  font-size: 24rpx;
  color: #999;
}

.status-modal-footer {
  display: flex;
  border-top: 1rpx solid #f0f0f0;
}

.status-btn {
  flex: 1;
  padding: 32rpx 0;
  font-size: 28rpx;
  border: none;
  background: none;
  cursor: pointer;
}

.cancel-btn {
  color: #666;
  border-right: 1rpx solid #f0f0f0;
}

.confirm-btn {
  color: #1890ff;
  font-weight: 500;
}

/* 动画效果 */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.info-card {
  animation: fadeIn 0.3s ease-out;
}

.vehicle-container .info-card:nth-child(1) {
  animation-delay: 0.1s;
}

.vehicle-container .info-card:nth-child(2) {
  animation-delay: 0.2s;
}

.vehicle-container .info-card:nth-child(3) {
  animation-delay: 0.3s;
}
